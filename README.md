# iChat AI Assistant

An AI-powered internal chat agent with document processing, vector search, and escalation workflow capabilities.

## 🚀 Features

- **AI Chat Interface**: Intelligent chat agent powered by OpenAI/Anthropic LLMs
- **Document Processing**: Upload and process PDF, Word, and text documents
- **Vector Search**: RAG (Retrieval-Augmented Generation) with pgvector
- **Escalation Workflow**: Human-in-the-loop learning system
- **Multi-Platform**: Web interface, Slack integration, and CLI tool
- **Real-time Communication**: WebSocket-based chat with typing indicators
- **User Management**: Role-based access control and authentication
- **Monitoring**: Comprehensive logging and health checks

## 🏗️ Architecture

This is a monorepo containing:

- **Backend**: Node.js + Express + TypeScript API server
- **Frontend**: React + TypeScript + Vite web application  
- **CLI**: TypeScript command-line interface (Docker-distributed)
- **Shared**: Common types, utilities, and constants

## 📋 Prerequisites

- Node.js 18+ and npm 9+
- Docker and Docker Compose
- PostgreSQL with pgvector extension
- Redis (for caching and sessions)

## 🛠️ Development Setup

### Option 1: Local Development

1. **Clone and install dependencies**:
   ```bash
   git clone <repository-url>
   cd ichat
   npm install
   ```

2. **Set up environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start services with Docker**:
   ```bash
   npm run docker:dev
   ```

4. **Set up database**:
   ```bash
   npm run db:setup
   ```

5. **Start development servers**:
   ```bash
   npm run dev
   ```

### Option 2: DevContainer (Recommended)

1. Open the project in VS Code
2. Install the "Dev Containers" extension
3. Press `Ctrl+Shift+P` and select "Dev Containers: Reopen in Container"
4. The development environment will be automatically set up

## 📦 Available Scripts

### Root Level
- `npm run dev` - Start all development servers
- `npm run build` - Build all packages
- `npm run test` - Run tests for all packages
- `npm run lint` - Lint all packages
- `npm run format` - Format code with Prettier
- `npm run docker:dev` - Start development environment with Docker

### Package-Specific
- `npm run dev:backend` - Start backend development server
- `npm run dev:frontend` - Start frontend development server
- `npm run build:backend` - Build backend
- `npm run build:frontend` - Build frontend
- `npm run test:backend` - Run backend tests

## 🌐 Services

When running in development mode:

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:3000
- **API Documentation**: http://localhost:3000/api/docs
- **Database**: localhost:5432
- **Redis**: localhost:6379

## 🐳 Docker Usage

### Development
```bash
# Start all services
docker-compose -f docker-compose.dev.yml up

# Start specific service
docker-compose -f docker-compose.dev.yml up backend

# View logs
docker-compose -f docker-compose.dev.yml logs -f backend
```

### Production
```bash
# Build and start
docker-compose up -d

# Scale services
docker-compose up -d --scale backend=2

# View status
docker-compose ps
```

### CLI Tool
```bash
# Build CLI container
docker-compose build cli

# Run CLI commands
docker-compose run --rm cli auth login
docker-compose run --rm cli docs upload document.pdf
docker-compose run --rm cli chat start
```

## 🗄️ Database

The project uses PostgreSQL with the pgvector extension for vector similarity search.

### Migrations
```bash
# Generate migration
npm run db:migrate --workspace=backend

# Apply migrations
npm run db:migrate:prod --workspace=backend

# View database
npm run db:studio --workspace=backend
```

### Schema
Key entities:
- Users (authentication and roles)
- Documents (uploaded files and content)
- DocumentChunks (text chunks with embeddings)
- ChatSessions (conversation threads)
- Messages (chat messages with confidence scores)
- EscalatedQueries (human escalation workflow)
- Feedback (user feedback on responses)

## 🔧 Configuration

### Environment Variables

Copy `.env.example` to `.env` and configure:

- **Database**: PostgreSQL connection details
- **Redis**: Redis connection for caching
- **LLM APIs**: OpenAI and Anthropic API keys
- **Authentication**: JWT secrets and OAuth configuration
- **File Upload**: Size limits and allowed types
- **Monitoring**: Logging levels and Application Insights

### LLM Configuration

The system supports multiple LLM providers:
- OpenAI (GPT-3.5, GPT-4)
- Anthropic (Claude 3 models)

Configure in environment variables or through the admin interface.

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch

# Run specific package tests
npm run test:backend
npm run test:frontend
```

## 📝 API Documentation

The backend API is documented with OpenAPI/Swagger. Access the interactive documentation at:
- Development: http://localhost:3000/api/docs
- Production: https://your-domain.com/api/docs

## 🚀 Deployment

### Azure Cloud (Recommended)

1. **Set up Azure resources**:
   - App Service for backend
   - Static Web Apps for frontend
   - Database for PostgreSQL
   - Container Registry for CLI
   - Key Vault for secrets

2. **Configure CI/CD**:
   - GitHub Actions or Azure DevOps
   - Automated testing and deployment
   - Environment-specific configurations

3. **Deploy**:
   ```bash
   # Build production images
   npm run build
   
   # Deploy to Azure
   az webapp up --name ichat-backend
   ```

### Docker Deployment

```bash
# Production deployment
docker-compose up -d

# With custom configuration
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

## 🔒 Security

- JWT-based authentication
- Role-based access control
- Input validation and sanitization
- Rate limiting
- Security headers (Helmet.js)
- CORS configuration
- Environment-based secrets

## 📊 Monitoring

- Winston logging with structured logs
- Health check endpoints
- Application metrics
- Error tracking
- Performance monitoring

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run linting and tests
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the documentation in `/docs`
- Review the project plan in `PROJECT_PLAN.md`
- Check technical specifications in `TECHNICAL_STACK.md`
- Open an issue on GitHub
