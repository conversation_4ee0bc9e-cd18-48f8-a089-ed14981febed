{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "types": ["vite/client", "vitest/globals", "@testing-library/jest-dom"], "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/pages/*": ["src/pages/*"], "@/hooks/*": ["src/hooks/*"], "@/services/*": ["src/services/*"], "@/store/*": ["src/store/*"], "@/types/*": ["src/types/*"], "@/utils/*": ["src/utils/*"], "@shared/*": ["../shared/src/*"]}}, "include": ["src"], "exclude": ["node_modules", "dist"], "references": [{"path": "./tsconfig.node.json"}]}