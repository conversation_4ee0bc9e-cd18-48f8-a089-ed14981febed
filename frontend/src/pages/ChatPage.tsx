export function ChatPage() {
  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 p-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">Chat</h1>
          
          <div className="card h-96">
            <div className="card-content flex items-center justify-center h-full">
              <p className="text-gray-500">Chat interface will be implemented here</p>
            </div>
          </div>
          
          <div className="mt-4">
            <div className="flex gap-2">
              <input
                type="text"
                placeholder="Type your message..."
                className="input flex-1"
              />
              <button className="btn-primary">Send</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
