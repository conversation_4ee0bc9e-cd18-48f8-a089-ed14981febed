{"name": "@ichat/frontend", "version": "1.0.0", "description": "iChat AI Assistant Frontend", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "react-hook-form": "^7.45.4", "react-query": "^3.39.3", "zustand": "^4.4.1", "socket.io-client": "^4.7.2", "axios": "^1.5.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "date-fns": "^2.30.0", "react-hot-toast": "^2.4.1", "react-markdown": "^8.0.7", "react-syntax-highlighter": "^15.5.0", "framer-motion": "^10.16.4"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-syntax-highlighter": "^15.5.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "@vitest/ui": "^0.34.4", "autoprefixer": "^10.4.15", "eslint": "^8.45.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "jsdom": "^22.1.0", "postcss": "^8.4.29", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "vite": "^4.4.5", "vitest": "^0.34.4", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.3", "@testing-library/user-event": "^14.4.3"}, "engines": {"node": ">=18.0.0"}}