# Environment Configuration
NODE_ENV=development

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/ichat
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=ichat
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# API Keys
OPENAI_API_KEY=sk-your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key

# LLM Configuration
DEFAULT_LLM_PROVIDER=openai
OPENAI_MODEL=gpt-3.5-turbo
ANTHROPIC_MODEL=claude-3-sonnet-20240229
EMBEDDING_MODEL=text-embedding-ada-002

# RAG Configuration
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_RETRIEVAL_RESULTS=5
CONFIDENCE_THRESHOLD=0.7

# File Upload Configuration
MAX_FILE_SIZE=********
ALLOWED_FILE_TYPES=pdf,docx,txt,md
UPLOAD_DIR=uploads

# Server Configuration
PORT=3000
HOST=localhost
CORS_ORIGIN=http://localhost:5173

# WebSocket Configuration
WEBSOCKET_PORT=3001

# Slack Integration
SLACK_BOT_TOKEN=xoxb-your-slack-bot-token
SLACK_SIGNING_SECRET=your-slack-signing-secret
SLACK_APP_TOKEN=xapp-your-slack-app-token

# Azure Configuration (for production)
AZURE_STORAGE_ACCOUNT=your-storage-account
AZURE_STORAGE_KEY=your-storage-key
AZURE_STORAGE_CONTAINER=documents
AZURE_KEY_VAULT_URL=https://your-keyvault.vault.azure.net/
AZURE_CLIENT_ID=your-azure-client-id
AZURE_CLIENT_SECRET=your-azure-client-secret
AZURE_TENANT_ID=your-azure-tenant-id

# OAuth2 Configuration
OAUTH_CLIENT_ID=your-oauth-client-id
OAUTH_CLIENT_SECRET=your-oauth-client-secret
OAUTH_REDIRECT_URI=http://localhost:3000/auth/callback
OAUTH_ISSUER=https://your-identity-provider.com

# Monitoring and Logging
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true
AZURE_APPLICATION_INSIGHTS_KEY=your-app-insights-key

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password
FROM_EMAIL=<EMAIL>

# CLI Configuration
CLI_API_URL=http://localhost:3000/api
CLI_TIMEOUT=30000
