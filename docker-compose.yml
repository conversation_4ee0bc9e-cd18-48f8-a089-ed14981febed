version: '3.8'

services:
  # PostgreSQL with pgvector extension
  db:
    image: pgvector/pgvector:pg15
    container_name: ichat-db
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-ichat}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - ichat-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: ichat-redis
    volumes:
      - redis_data:/data
    networks:
      - ichat-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API server
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ichat-backend
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD}@db:5432/${POSTGRES_DB:-ichat}
      - REDIS_URL=redis://redis:6379
      - PORT=3000
      - JWT_SECRET=${JWT_SECRET}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
    ports:
      - "3000:3000"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - ichat-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend web application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: ichat-frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - ichat-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # CLI tool (for administrative tasks)
  cli:
    build:
      context: ./cli
      dockerfile: Dockerfile
    container_name: ichat-cli
    environment:
      - CLI_API_URL=http://backend:3000/api
    volumes:
      - ./data:/workspace
      - cli_config:/root/.ichat
    networks:
      - ichat-network
    profiles:
      - cli
    restart: "no"

volumes:
  postgres_data:
  redis_data:
  cli_config:

networks:
  ichat-network:
    driver: bridge
