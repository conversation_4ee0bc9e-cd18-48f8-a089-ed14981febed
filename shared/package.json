{"name": "@ichat/shared", "version": "1.0.0", "description": "Shared types and utilities for iChat AI Assistant", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {}, "devDependencies": {"@types/node": "^20.5.9", "@types/jest": "^29.5.4", "jest": "^29.6.4", "ts-jest": "^29.1.1", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/index.ts"]}, "files": ["dist/**/*", "README.md"]}