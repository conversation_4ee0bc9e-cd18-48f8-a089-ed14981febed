// User types
export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  createdAt: string;
  updatedAt: string;
}

export enum UserRole {
  // eslint-disable-next-line no-unused-vars
  USER = 'USER',
  // eslint-disable-next-line no-unused-vars
  ADMIN = 'ADMIN',
  // eslint-disable-next-line no-unused-vars
  MANAGER = 'MANAGER',
}

// Document types
export interface Document {
  id: string;
  title: string;
  content: string;
  filePath: string;
  mimeType: string;
  fileSize: number;
  uploadedBy: string;
  createdAt: string;
  updatedAt: string;
}

// Chat types
export interface ChatSession {
  id: string;
  userId: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  messages?: Message[];
}

export interface Message {
  id: string;
  sessionId: string;
  content: string;
  role: MessageRole;
  confidence?: number;
  escalated: boolean;
  metadata?: Record<string, unknown>;
  createdAt: string;
}

export enum MessageRole {
  // eslint-disable-next-line no-unused-vars
  USER = 'USER',
  // eslint-disable-next-line no-unused-vars
  ASSISTANT = 'ASSISTANT',
}

// Escalation types
export interface EscalatedQuery {
  id: string;
  messageId: string;
  status: EscalationStatus;
  assignedTo?: string;
  resolution?: string;
  createdAt: string;
  resolvedAt?: string;
  updatedAt: string;
}

export enum EscalationStatus {
  // eslint-disable-next-line no-unused-vars
  OPEN = 'OPEN',
  // eslint-disable-next-line no-unused-vars
  IN_PROGRESS = 'IN_PROGRESS',
  // eslint-disable-next-line no-unused-vars
  RESOLVED = 'RESOLVED',
  // eslint-disable-next-line no-unused-vars
  CLOSED = 'CLOSED',
}

// Feedback types
export interface Feedback {
  id: string;
  messageId: string;
  userId: string;
  type: FeedbackType;
  rating?: number;
  comment?: string;
  createdAt: string;
}

export enum FeedbackType {
  // eslint-disable-next-line no-unused-vars
  THUMBS_UP = 'THUMBS_UP',
  // eslint-disable-next-line no-unused-vars
  THUMBS_DOWN = 'THUMBS_DOWN',
  // eslint-disable-next-line no-unused-vars
  DETAILED = 'DETAILED',
}

// API Response types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    code?: string;
    details?: unknown;
  };
  timestamp: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// WebSocket types
export interface WebSocketMessage {
  type: string;
  payload: unknown;
  timestamp: string;
}

export interface ChatMessage extends WebSocketMessage {
  type: 'chat:message';
  payload: {
    sessionId: string;
    message: Message;
  };
}

export interface TypingIndicator extends WebSocketMessage {
  type: 'chat:typing';
  payload: {
    sessionId: string;
    userId: string;
    isTyping: boolean;
  };
}

// Configuration types
export interface LLMConfig {
  provider: 'openai' | 'anthropic';
  model: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
}

export interface RAGConfig {
  chunkSize: number;
  chunkOverlap: number;
  maxRetrievalResults: number;
  confidenceThreshold: number;
}

// Error types
export interface AppError {
  message: string;
  code?: string;
  statusCode?: number;
  details?: unknown;
}
