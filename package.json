{"name": "ichat-ai-assistant", "version": "1.0.0", "description": "AI-powered internal chat agent with document processing and escalation workflow", "private": true, "workspaces": ["backend", "frontend", "cli", "shared"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "npm run dev --workspace=backend", "dev:frontend": "npm run dev --workspace=frontend", "dev:cli": "npm run dev --workspace=cli", "build": "npm run build --workspaces", "build:backend": "npm run build --workspace=backend", "build:frontend": "npm run build --workspace=frontend", "build:cli": "npm run build --workspace=cli", "build:shared": "npm run build --workspace=shared", "test": "npm run test --workspaces", "test:backend": "npm run test --workspace=backend", "test:frontend": "npm run test --workspace=frontend", "test:cli": "npm run test --workspace=cli", "lint": "npm run lint --workspaces", "lint:fix": "npm run lint:fix --workspaces", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "type-check": "npm run type-check --workspaces", "clean": "npm run clean --workspaces && rm -rf node_modules", "setup:dev": "npm install && npm run build:shared && npm run db:setup", "db:setup": "npm run db:setup --workspace=backend", "db:migrate": "npm run db:migrate --workspace=backend", "db:seed": "npm run db:seed --workspace=backend", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:dev": "docker-compose -f docker-compose.dev.yml up", "prepare": "husky install"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "concurrently": "^8.2.0", "eslint": "^8.49.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.28.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^8.0.3", "lint-staged": "^14.0.1", "prettier": "^3.0.3", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/company/ichat-ai-assistant.git"}, "keywords": ["ai", "chat", "assistant", "typescript", "node.js", "react", "postgresql", "vector-search"], "author": "Company Development Team", "license": "MIT"}