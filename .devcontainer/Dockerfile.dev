FROM node:18-bullseye

# Install additional system packages
RUN apt-get update && apt-get install -y \
    git \
    curl \
    vim \
    nano \
    wget \
    unzip \
    postgresql-client \
    redis-tools \
    python3 \
    python3-pip \
    build-essential \
    jq \
    tree \
    htop \
    && rm -rf /var/lib/apt/lists/*

# Install global npm packages for development
RUN npm install -g \
    typescript \
    ts-node \
    nodemon \
    prisma \
    @azure/cli \
    concurrently \
    npm-check-updates

# Create non-root user
ARG USERNAME=node
ARG USER_UID=1000
ARG USER_GID=$USER_UID

# Ensure the node user has the correct UID/GID
RUN groupmod --gid $USER_GID $USERNAME \
    && usermod --uid $USER_UID --gid $USER_GID $USERNAME \
    && chown -R $USER_UID:$USER_GID /home/<USER>

# Add node user to sudo group (for development convenience)
RUN apt-get update && apt-get install -y sudo \
    && echo $USERNAME ALL=\(root\) NOPASSWD:ALL > /etc/sudoers.d/$USERNAME \
    && chmod 0440 /etc/sudoers.d/$USERNAME \
    && rm -rf /var/lib/apt/lists/*

# Set up workspace
WORKDIR /workspace

# Switch to non-root user
USER $USERNAME

# Set up shell environment
RUN echo 'alias ll="ls -la"' >> ~/.bashrc \
    && echo 'alias la="ls -la"' >> ~/.bashrc \
    && echo 'alias ..="cd .."' >> ~/.bashrc \
    && echo 'export PATH="$PATH:/workspace/node_modules/.bin"' >> ~/.bashrc

# Default command
CMD ["sleep", "infinity"]
