version: '3.8'

services:
  devcontainer:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: ichat-devcontainer
    volumes:
      - ../..:/workspace:cached
      - node_modules:/workspace/node_modules
      - backend_node_modules:/workspace/backend/node_modules
      - frontend_node_modules:/workspace/frontend/node_modules
      - cli_node_modules:/workspace/cli/node_modules
      - shared_node_modules:/workspace/shared/node_modules
    environment:
      - NODE_ENV=development
      - DATABASE_URL=*****************************************/ichat_dev
      - REDIS_URL=redis://redis:6379
    command: sleep infinity
    depends_on:
      - db
      - redis
    networks:
      - ichat-dev-network

  db:
    image: pgvector/pgvector:pg15
    container_name: ichat-devcontainer-db
    environment:
      POSTGRES_DB: ichat_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: devpassword
    volumes:
      - postgres_data_devcontainer:/var/lib/postgresql/data
    networks:
      - ichat-dev-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: ichat-devcontainer-redis
    volumes:
      - redis_data_devcontainer:/data
    networks:
      - ichat-dev-network
    restart: unless-stopped

volumes:
  node_modules:
  backend_node_modules:
  frontend_node_modules:
  cli_node_modules:
  shared_node_modules:
  postgres_data_devcontainer:
  redis_data_devcontainer:

networks:
  ichat-dev-network:
    name: ichat-dev-network
    external: false
