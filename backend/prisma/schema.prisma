// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  role      UserRole @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  chatSessions    ChatSession[]
  uploadedDocuments Document[]
  escalatedQueries  EscalatedQuery[]
  feedbacks        Feedback[]

  @@map("users")
}

model Document {
  id          String   @id @default(cuid())
  title       String
  content     String
  filePath    String
  mimeType    String
  fileSize    Int
  uploadedBy  String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  uploader    User @relation(fields: [uploadedBy], references: [id], onDelete: Cascade)
  chunks      DocumentChunk[]

  @@map("documents")
}

model DocumentChunk {
  id         String                @id @default(cuid())
  documentId String
  content    String
  embedding  Unsupported("vector")?
  metadata   Json?
  chunkIndex Int
  createdAt  DateTime              @default(now())

  // Relations
  document Document @relation(fields: [documentId], references: [id], onDelete: Cascade)

  @@map("document_chunks")
}

model ChatSession {
  id        String   @id @default(cuid())
  userId    String
  title     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages Message[]

  @@map("chat_sessions")
}

model Message {
  id         String      @id @default(cuid())
  sessionId  String
  content    String
  role       MessageRole
  confidence Float?
  escalated  Boolean     @default(false)
  metadata   Json?
  createdAt  DateTime    @default(now())

  // Relations
  session          ChatSession      @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  escalatedQuery   EscalatedQuery?
  feedbacks        Feedback[]

  @@map("messages")
}

model EscalatedQuery {
  id         String              @id @default(cuid())
  messageId  String              @unique
  status     EscalationStatus    @default(OPEN)
  assignedTo String?
  resolution String?
  createdAt  DateTime            @default(now())
  resolvedAt DateTime?
  updatedAt  DateTime            @updatedAt

  // Relations
  message  Message @relation(fields: [messageId], references: [id], onDelete: Cascade)
  assignee User?   @relation(fields: [assignedTo], references: [id])

  @@map("escalated_queries")
}

model Feedback {
  id        String       @id @default(cuid())
  messageId String
  userId    String
  type      FeedbackType
  rating    Int?         // 1-5 scale
  comment   String?
  createdAt DateTime     @default(now())

  // Relations
  message Message @relation(fields: [messageId], references: [id], onDelete: Cascade)
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([messageId, userId])
  @@map("feedbacks")
}

// Enums
enum UserRole {
  USER
  ADMIN
  MANAGER
}

enum MessageRole {
  USER
  ASSISTANT
}

enum EscalationStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
}

enum FeedbackType {
  THUMBS_UP
  THUMBS_DOWN
  DETAILED
}
