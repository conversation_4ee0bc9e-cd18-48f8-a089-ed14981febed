{"name": "@ichat/backend", "version": "1.0.0", "description": "iChat AI Assistant Backend API", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:seed": "ts-node prisma/seed.ts", "db:studio": "prisma studio", "db:setup": "npm run db:generate && npm run db:migrate && npm run db:seed"}, "dependencies": {"@prisma/client": "^5.3.1", "@anthropic-ai/sdk": "^0.6.0", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "cors": "^2.8.5", "helmet": "^7.0.0", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.10.0", "socket.io": "^4.7.2", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "joi": "^17.9.2", "multer": "^1.4.5-lts.1", "sharp": "^0.32.5", "file-type": "^18.5.0", "pdf-parse": "^1.1.1", "mammoth": "^1.5.1", "openai": "^4.8.0", "langchain": "^0.0.140", "@langchain/openai": "^0.0.14", "@langchain/community": "^0.0.25", "dotenv": "^16.3.1", "express-async-errors": "^3.1.1", "http-status-codes": "^2.2.0", "ioredis": "^5.3.2", "node-cron": "^3.0.2", "nodemailer": "^6.9.4", "uuid": "^9.0.0"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/compression": "^1.7.2", "@types/morgan": "^1.9.4", "@types/jsonwebtoken": "^9.0.2", "@types/bcryptjs": "^2.4.2", "@types/multer": "^1.4.7", "@types/pdf-parse": "^1.1.1", "@types/node": "^20.5.9", "@types/node-cron": "^3.0.8", "@types/nodemailer": "^6.4.9", "@types/uuid": "^9.0.2", "@types/jest": "^29.5.4", "@types/supertest": "^2.0.12", "jest": "^29.6.4", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "prisma": "^5.3.1", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src", "<rootDir>/tests"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/index.ts"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}}