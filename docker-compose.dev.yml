version: '3.8'

services:
  # PostgreSQL with pgvector extension
  db:
    image: pgvector/pgvector:pg15
    container_name: ichat-db-dev
    environment:
      POSTGRES_DB: ichat_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: devpassword
    ports:
      - "5432:5432"
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
      - ./backend/prisma/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: ichat-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data_dev:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend development server
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: ichat-backend-dev
    environment:
      - NODE_ENV=development
      - DATABASE_URL=*****************************************/ichat_dev
      - REDIS_URL=redis://redis:6379
      - PORT=3000
      - CORS_ORIGIN=http://localhost:5173
      - LOG_LEVEL=debug
      - ENABLE_REQUEST_LOGGING=true
    ports:
      - "3000:3000"
    volumes:
      - ./backend:/app
      - /app/node_modules
      - ./shared:/shared
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: npm run dev

  # Frontend development server
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: ichat-frontend-dev
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:3000
      - VITE_WS_URL=ws://localhost:3000
    ports:
      - "5173:5173"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - ./shared:/shared
    depends_on:
      - backend
    command: npm run dev

volumes:
  postgres_data_dev:
  redis_data_dev:

networks:
  default:
    name: ichat-dev-network
