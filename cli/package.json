{"name": "@ichat/cli", "version": "1.0.0", "description": "iChat AI Assistant Command Line Interface", "main": "dist/index.js", "bin": {"ichat-cli": "dist/index.js"}, "scripts": {"dev": "ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist", "link": "npm link", "unlink": "npm unlink -g @ichat/cli"}, "dependencies": {"commander": "^11.0.0", "inquirer": "^9.2.10", "chalk": "^5.3.0", "ora": "^7.0.1", "table": "^6.8.1", "axios": "^1.5.0", "dotenv": "^16.3.1", "js-yaml": "^4.1.0", "fs-extra": "^11.1.1", "mime-types": "^2.1.35", "progress": "^2.0.3", "ws": "^8.13.0"}, "devDependencies": {"@types/node": "^20.5.9", "@types/inquirer": "^9.0.3", "@types/js-yaml": "^4.0.5", "@types/fs-extra": "^11.0.1", "@types/mime-types": "^2.1.1", "@types/progress": "^2.0.5", "@types/ws": "^8.5.5", "@types/jest": "^29.5.4", "jest": "^29.6.4", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src", "<rootDir>/tests"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/index.ts"]}, "files": ["dist/**/*", "README.md"], "keywords": ["cli", "ai", "chat", "assistant", "typescript"]}