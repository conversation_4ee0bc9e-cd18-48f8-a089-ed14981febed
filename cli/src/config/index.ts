import fs from 'fs';
import os from 'os';
import path from 'path';

interface CLIConfig {
  apiUrl: string;
  apiKey?: string;
  timeout: number;
  format: 'json' | 'table' | 'yaml';
}

export class ConfigManager {
  private configPath: string;
  private config!: CLIConfig;

  constructor() {
    this.configPath = path.join(os.homedir(), '.ichat', 'config.json');
    this.loadConfig();
  }

  private loadConfig(): void {
    const defaultConfig: CLIConfig = {
      apiUrl: process.env.CLI_API_URL || 'http://localhost:3000/api',
      timeout: parseInt(process.env.CLI_TIMEOUT || '30000'),
      format: 'table',
    };

    try {
      if (fs.existsSync(this.configPath)) {
        const fileConfig = JSON.parse(fs.readFileSync(this.configPath, 'utf8'));
        this.config = { ...defaultConfig, ...fileConfig };
      } else {
        this.config = defaultConfig;
        this.saveConfig();
      }
    } catch (error) {
      console.warn('Warning: Could not load config file, using defaults');
      this.config = defaultConfig;
    }
  }

  public get<K extends keyof CLIConfig>(key: K): CLIConfig[K] {
    return this.config[key];
  }

  public set<K extends keyof CLIConfig>(key: K, value: CLIConfig[K]): void {
    this.config[key] = value;
    this.saveConfig();
  }

  public getAll(): CLIConfig {
    return { ...this.config };
  }

  private saveConfig(): void {
    try {
      const configDir = path.dirname(this.configPath);
      if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true });
      }
      fs.writeFileSync(this.configPath, JSON.stringify(this.config, null, 2));
    } catch (error) {
      console.warn('Warning: Could not save config file');
    }
  }

  public isAuthenticated(): boolean {
    return !!this.config.apiKey;
  }
}
