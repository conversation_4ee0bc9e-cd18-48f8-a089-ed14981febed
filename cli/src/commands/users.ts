import { Command } from 'commander';

export class UsersCommand {
  public getCommand(): Command {
    const usersCommand = new Command('users');
    usersCommand.description('User management commands');

    usersCommand
      .command('create <email>')
      .description('Create a new user')
      .option('-n, --name <name>', 'User name')
      .option('-r, --role <role>', 'User role (user|admin|manager)', 'user')
      .action((email, options) => {
        console.log(`Creating user: ${email}`);
        console.log(`Name: ${options.name || 'Not specified'}`);
        console.log(`Role: ${options.role}`);
        // TODO: Implement user creation
      });

    usersCommand
      .command('list')
      .description('List all users')
      .action(() => {
        console.log('Listing users...');
        // TODO: Implement user listing
      });

    return usersCommand;
  }
}
