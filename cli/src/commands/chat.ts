import { Command } from 'commander';

export class ChatCommand {
  public getCommand(): Command {
    const chatCommand = new Command('chat');
    chatCommand.description('Interactive chat commands');

    chatCommand
      .command('start')
      .description('Start an interactive chat session')
      .action(() => {
        console.log('Starting interactive chat...');
        console.log('Type "exit" to quit');
        // TODO: Implement interactive chat
      });

    return chatCommand;
  }
}
