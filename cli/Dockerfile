# Multi-stage build for CLI
FROM node:18-alpine AS build

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Install dependencies
RUN npm ci

# Copy source code
COPY src ./src

# Build the application
RUN npm run build

# Production stage
FROM node:18-alpine AS runtime

# Install curl for health checks
RUN apk add --no-cache curl

# Create app user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Set working directory
WORKDIR /app

# Copy package files and install production dependencies
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Copy built application
COPY --from=build --chown=nodejs:nodejs /app/dist ./dist

# Create CLI executable link
RUN npm link

# Create workspace and config directories
RUN mkdir -p /workspace /root/.ichat
RUN chown -R nodejs:nodejs /workspace /root/.ichat

# Switch to workspace directory
WORKDIR /workspace

# Switch to non-root user
USER nodejs

# Set entrypoint to CLI
ENTRYPOINT ["ichat-cli"]

# Default command shows help
CMD ["--help"]
